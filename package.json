{"name": "livekit", "version": "1.0.0", "description": "TODO: Give a short introduction of your project. Let this section explain the objectives or the motivation behind this project.", "main": "index.js", "type": "module", "scripts": {"test": "echo \"Error: no test specified\" && exit 1", "start": "node server.js"}, "repository": {"type": "git", "url": "https://<EMAIL>/playsavvyapp/savvy/_git/livekit-server"}, "author": "", "license": "ISC", "dependencies": {"@azure/identity": "^4.10.0", "@azure/keyvault-secrets": "^4.9.0", "dotenv": "^16.4.7", "firebase-admin": "^13.2.0", "livekit-server-sdk": "^2.11.0", "restify": "^11.1.0"}}
import dotenv from 'dotenv';
import * as restify from 'restify';
import admin from 'firebase-admin';
import { verifyFirebaseToken } from './middlewares.js';
import { createToken } from './tokenGenerator.js';
import { getSecret } from './getSecret.js';

dotenv.config();

(async function startServer() {
  const serviceAccount = await getSecret('firebase-credentials-json');

  admin.initializeApp({
    credential: admin.credential.cert(serviceAccount),
  });

  const server = restify.createServer();
  server.use(restify.plugins.bodyParser());

  // 🎯 Route: Create token
  server.post('/createToken', verifyFirebaseToken, async (req, res) => {
    const { roomName } = req.body;

    if (!roomName) {
      return res.send(400, { error: 'Missing required field: roomName' });
    }

    const identity = req.firebaseUser.uid;
    const role = req.firebaseUser.role || 'user';

    try {
      const token = await createToken({ roomName, identity, role });
      res.send({ token });
    } catch (err) {
      console.error('Failed to create token:', err);
      res.send(500, { error: 'Could not generate token' });
    }
  });

  // 🚀 Start server
  const port = process.env.PORT || process.env.port || 80;
  server.listen(port, () => {
    console.log(`Server listening on port ${port}`);
  });
})();

import { AccessToken } from 'livekit-server-sdk';

export const createToken = async ({ roomName, identity, role }) => {
  const at = new AccessToken(await getSecret('livekit-api-key'), await getSecret('livekit-api-secret'), {
    identity,
    ttl: '30m',
  });

  const grant = {
    roomJoin: true,
    room: roomName,
    canSubscribe: true,
    canUpdateOwnMetadata: true,
    canPublishData: true,
  };

  if (role === 'host') {
    grant.roomCreate = true;
    grant.canPublish = true;
  }else{
    grant.roomCreate = false;
    grant.canPublish = false;
  }

  at.addGrant(grant);
  return await at.toJwt();
};
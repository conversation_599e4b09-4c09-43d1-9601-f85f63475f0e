# Docker
# Build and push an image to Azure Container Registry
# https://docs.microsoft.com/azure/devops/pipelines/languages/docker

trigger:
- main
- develop

resources:
- repo: self

variables:
- group : savvy-token-server-dev
- group : savvy-common-dev
- name : dockerfilePath
  value: '**/Dockerfile'
- name: tag
  value: $[counter('livekit-server-dev', 1)]

stages:
- stage: Build
  displayName: Build and push stage
  jobs:
  - job: Build
    displayName: Build
    pool:
      vmImage: $(vmImageName)
    steps:
    - task: Docker@2
      displayName: 'Build and Push Docker Image'
      inputs:
        command: buildAndPush
        repository: $(imageRepository)
        dockerfile: $(dockerfilePath)
        containerRegistry: $(dockerRegistryServiceConnection)
        tags: |
          $(tag)
- stage: Deployment
  displayName: Deployment

  jobs:
    - job: Deploy
      displayName: Deploy to Azure App Services
      pool:
         vmImage: $(vmImageName)
      steps:
      - task: AzureWebAppContainer@1
        displayName: 'Deploy Docker Image to Azure App Service'
        inputs:
          azureSubscription: $(azureServiceConnection)
          appName: $(appName)
          containers: '$(containerRegistry)/$(imageRepository):$(tag)'

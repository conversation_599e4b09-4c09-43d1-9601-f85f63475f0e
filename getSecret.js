const { SecretClient } = require("@azure/keyvault-secrets");
const { DefaultAzureCredential } = require("@azure/identity");

const vaultUrl = process.env.KEY_VAULT_URI;

const credential = new DefaultAzureCredential();
const client = new SecretClient(vaultUrl, credential);

export async function getSecret(secretName) {
  try {
    const secret = await client.getSecret(secretName);
    console.log(`Fetched secret: ${secretName} = ${secret.value}`);
    return secret.value;
  } catch (err) {
    console.error(`Failed to get secret '${secretName}':`, err.message);
    throw err;
  }
}
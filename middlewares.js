import admin from 'firebase-admin';

function getIPv4(remoteAddress) {
  // Handle IPv6-mapped addresses (::ffff:********* → *********)
  if (remoteAddress.startsWith('::ffff:')) {
    return remoteAddress.split(':').pop();
  }

  // Handle IPv6 loopback (::1 → 127.0.0.1)
  if (remoteAddress === '::1') {
    return '127.0.0.1';
  }

  // If addres already IPv4
  if (/^\d+\.\d+\.\d+\.\d+$/.test(remoteAddress)) {
    return remoteAddress;
  }

  return null;
}

export const verifyFirebaseToken = async (req, res) => {
  const clientIp = req.headers['x-forwarded-for']?.split(',')[0] || req.socket.remoteAddress;
  const ipv4 = getIPv4(clientIp);

  console.info(`Authentication start. Remote IP: ${ipv4}, Method: ${req.route.method}, Path: ${req.route.path}`);

  const authHeader = req.header('Authorization');

  if (!authHeader || !authHeader.startsWith('Bearer ')) {
    console.warn(`Missing Authorization. Remote IP: ${ipv4}, Method: ${req.route.method}, Path: ${req.route.path}`);
    return res.send(401, { error: 'Missing or invalid Authorization header' });
  }

  try {
    const token = authHeader.split('Bearer ')[1];
    
    const decodedToken = await admin.auth().verifyIdToken(token);

    console.info(`Successful Authentication. Remote IP: ${ipv4}, Method: ${req.route.method}, Path: ${req.route.path}, UID: ${decodedToken.uid}`);

    req.firebaseUser = decodedToken;
  } catch (error) {
    console.error(`Unauthorized. Remote IP: ${ipv4}, Method: ${req.route.method}, Path: ${req.route.path}, Exception: ${error.message}`);
    return res.send(401, { error: 'Invalid or expired Firebase ID token' });
  }
};
